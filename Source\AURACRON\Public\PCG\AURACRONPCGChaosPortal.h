// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "Engine/DataTable.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Net/UnrealNetwork.h"
#include "AURACRONPCGChaosPortal.generated.h"

class UStaticMeshComponent;
class UNiagaraComponent;
class UPointLightComponent;
class UAudioComponent;
class USphereComponent;
class UMaterialInstanceDynamic;
class UNiagaraSystem;
class USoundBase;

/**
 * Tipos de portais de caos
 */
UENUM(BlueprintType)
enum class EChaosPortalType : uint8
{
    Standard UMETA(DisplayName = "Padrão"),
    Elite UMETA(DisplayName = "Elite"),
    Legendary UMETA(DisplayName = "Lendário")
};

/**
 * Tipos de perigos ambientais
 */
UENUM(BlueprintType)
enum class EEnvironmentalHazardType : uint8
{
    Energy UMETA(DisplayName = "Energético"),
    Toxic UMETA(DisplayName = "Tóxico"),
    Volcanic UMETA(DisplayName = "Vulcânico"),
    Frost UMETA(DisplayName = "Gelo"),
    Lightning UMETA(DisplayName = "Raio")
};

/**
 * Estrutura para recompensas de alto risco
 */
USTRUCT(BlueprintType)
struct AURACRON_API FChaosPortalHighRiskReward : public FTableRowBase
{
    GENERATED_BODY()

    /** Nome da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    FString RewardName;

    /** Tier da recompensa (1-5) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    int32 RewardTier = 1;

    /** Classe do item a ser spawnado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    TSoftClassPtr<AActor> RewardItemClass;

    /** Probabilidade de spawn (0.0-1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float SpawnProbability = 0.5f;

    /** Valor da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    int32 RewardValue = 100;

    /** Cor do glow da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    FLinearColor GlowColor = FLinearColor::White;

    /** Efeito VFX da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    TSoftObjectPtr<UNiagaraSystem> RewardVFX;

    /** Som da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    TSoftObjectPtr<USoundBase> RewardSound;

    FChaosPortalHighRiskReward()
    {
        RewardName = TEXT("Unknown Reward");
        RewardTier = 1;
        SpawnProbability = 0.5f;
        RewardValue = 100;
        GlowColor = FLinearColor::White;
    }
};

/**
 * Estrutura para perigos ambientais
 */
USTRUCT(BlueprintType)
struct AURACRON_API FChaosPortalEnvironmentalHazard : public FTableRowBase
{
    GENERATED_BODY()

    /** Nome do perigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    FString HazardName;

    /** Tipo do perigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    EEnvironmentalHazardType HazardType = EEnvironmentalHazardType::Energy;

    /** Classe do ator de perigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    TSoftClassPtr<AActor> HazardActorClass;

    /** Intensidade do perigo (1.0-5.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    float HazardIntensity = 1.0f;

    /** Duração do perigo em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    float HazardDuration = 15.0f;

    /** Raio de efeito do perigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    float EffectRadius = 300.0f;

    /** Dano por segundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    float DamagePerSecond = 10.0f;

    /** Efeito VFX do perigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    TSoftObjectPtr<UNiagaraSystem> HazardVFX;

    /** Som do perigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    TSoftObjectPtr<USoundBase> HazardSound;

    /** Cor do perigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hazard")
    FLinearColor HazardColor = FLinearColor::Red;

    FChaosPortalEnvironmentalHazard()
    {
        HazardName = TEXT("Unknown Hazard");
        HazardType = EEnvironmentalHazardType::Energy;
        HazardIntensity = 1.0f;
        HazardDuration = 15.0f;
        EffectRadius = 300.0f;
        DamagePerSecond = 10.0f;
        HazardColor = FLinearColor::Red;
    }
};

/**
 * Ator que representa um portal de caos no mapa
 * Implementa o efeito especial ChaosPortals para a Fase 4 (Resolução)
 * 
 * Integração com Ilhas Caos:
 * - Localização: Em pontos de interseção do Fluxo
 * - Características: Perigos ambientais, recompensas de alto risco, terreno instável
 * - Valor Estratégico: Itens que mudam o jogo com risco significativo
 */
UCLASS()
class AURACRON_API AAURACRONPCGChaosPortal : public AActor
{
    GENERATED_BODY()

public:
    // Sets default values for this actor's properties
    AAURACRONPCGChaosPortal();

    // Called when the game starts or when spawned
    virtual void BeginPlay() override;

    // Called when the actor is being destroyed or removed from world
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    // Called every frame
    virtual void Tick(float DeltaTime) override;

    /** Ativar portal com duração e intensidade específicas */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void ActivatePortal(float Duration = 0.0f, float Intensity = 1.0f);

    /** Desativar portal com fade out */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void DeactivatePortal(float FadeOutTime = 1.0f);

    /** Configurar escala de qualidade (para ajuste de performance) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetQualityScale(float NewQualityScale);

    /** Atualizar portal para fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);

    /** Disparar efeito do portal com probabilidade */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void TriggerPortalEffect(float SpawnProbability = 1.0f);
    
    /** Gerar recompensa de alto risco */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SpawnHighRiskReward(float RewardTier = 1.0f);
    
    /** Ativar perigo ambiental */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void ActivateEnvironmentalHazard(float HazardIntensity = 1.0f);
    
    /** Ativar instabilidade de terreno */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void ActivateTerrainInstability(float InstabilityIntensity = 1.0f);

    /** Configurar raio do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalRadius(float Radius) { EffectRadius = Radius; }

    /** Configurar intensidade do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalIntensity(float Intensity) { PortalIntensity = Intensity; }

    /** Configurar tempo de vida do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalLifetime(float Lifetime) { PortalDuration = Lifetime; }

    /** Configurar tipo do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalType(EChaosPortalType Type);

protected:
    /** Componente de malha estática */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UStaticMeshComponent* PortalMesh;

    /** Componente de efeito de partículas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UNiagaraComponent* PortalEffect;

    /** Componente de luz */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UPointLightComponent* PortalLight;

    /** Componente de áudio */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UAudioComponent* PortalSound;

    /** Componente de colisão para trigger */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    USphereComponent* TriggerSphere;

    /** Material dinâmico do portal */
    UPROPERTY()
    UMaterialInstanceDynamic* PortalDynamicMaterial;

    /** Raio de efeito do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float EffectRadius;

    /** Duração do portal (0 = permanente) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PortalDuration;

    /** Intensidade do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PortalIntensity;

    /** Cor do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    FLinearColor PortalColor;

    /** Velocidade de rotação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float RotationSpeed;

    /** Frequência de pulsação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PulsateFrequency;

    /** Intensidade de pulsação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PulsateIntensity;

    /** Intervalo entre efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float EffectInterval;

    /** Escala de qualidade (para ajuste de performance) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float QualityScale;

    /** Tipo do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    EChaosPortalType PortalType;
    
    /** Tabela de recompensas de alto risco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    TObjectPtr<UDataTable> HighRiskRewardsTable;

    /** Tabela de perigos ambientais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    TObjectPtr<UDataTable> EnvironmentalHazardsTable;

    /** Probabilidade de gerar perigo ambiental */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float EnvironmentalHazardProbability;
    
    /** Probabilidade de instabilidade de terreno */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float TerrainInstabilityProbability;
    
    /** Probabilidade de recompensa de alto risco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float HighRiskRewardProbability;

    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    /** Orçamento de partículas baseado no hardware (Entry/Mid/High) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal|Performance")
    int32 ParticleBudget;

    /** Configuração de qualidade de hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal|Performance")
    FString HardwareQuality;

    /** StreamableManager para carregamento assíncrono UE 5.6 */
    UPROPERTY()
    TObjectPtr<UStreamableManager> StreamableManager;

    /** Replicação multiplayer - se o portal está ativo */
    UPROPERTY(ReplicatedUsing = OnRep_PortalActive)
    bool bPortalActiveReplicated;

    /** Replicação multiplayer - intensidade do portal */
    UPROPERTY(ReplicatedUsing = OnRep_PortalIntensity)
    float PortalIntensityReplicated;

    /** Callback para replicação de ativação */
    UFUNCTION()
    void OnRep_PortalActive();

    /** Callback para replicação de intensidade */
    UFUNCTION()
    void OnRep_PortalIntensity();

    /** Configurar propriedades replicadas */
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

private:
    /** Tempo decorrido desde ativação */
    float ElapsedTime;

    /** Tempo desde último efeito */
    float TimeSinceLastEffect;

    /** Se o portal está ativo */
    bool bPortalActive;

    /** Se está em fade out */
    bool bFadingOut;

    /** Tempo de fade out */
    float FadeOutTime;

    /** Tempo decorrido de fade out */
    float FadeOutElapsed;

    /** Timer handles para otimização UE 5.6 */
    FTimerHandle VisualEffectsTimerHandle;
    FTimerHandle EffectTriggerTimerHandle;
    FTimerHandle ExpirationTimerHandle;

    /** Atualizar efeitos visuais */
    void UpdateVisualEffects(float DeltaTime);

    /** Aplicar efeitos aos jogadores */
    void ApplyEffectsToPlayers();

    /** Processar fade out */
    void ProcessFadeOut(float DeltaTime);

    /** Callback quando jogador entra no raio do portal */
    UFUNCTION()
    void OnPlayerEnterPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                 UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                                 bool bFromSweep, const FHitResult& SweepResult);

    /** Callback quando jogador sai do raio do portal */
    UFUNCTION()
    void OnPlayerExitPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
    
    /** Gerar efeito visual de perigo ambiental */
    UFUNCTION()
    void SpawnEnvironmentalHazardVisual(FVector Location, float Intensity);
    
    /** Gerar efeito visual de instabilidade de terreno */
    UFUNCTION()
    void SpawnTerrainInstabilityVisual(FVector Location, float Intensity);
    
    /** Gerar efeito visual de recompensa de alto risco */
    UFUNCTION()
    void SpawnHighRiskRewardVisual(FVector Location, float RewardTier);
    
    /** Verificar se o portal está em um ponto de interseção do Fluxo */
    UFUNCTION()
    bool IsAtFlowIntersection() const;

    /** Funções de Timer otimizadas para UE 5.6 */
    void StartPortalTimers();
    void StopPortalTimers();
    void UpdateVisualEffectsTimer();
    void CheckEffectTrigger();
    void OnPortalExpired();

    /** Integração com sistema de Fluxo Prismal */
    void UpdateFluxPrismalIntegration();
    void HandleFluxPrismalPhaseChange(EAURACRONMapPhase NewPhase);

    /** Configurar orçamento de partículas baseado no hardware */
    void ConfigureParticleBudgetForHardware();

    /** Carregamento assíncrono de assets usando StreamableManager */
    void LoadPortalAssetsAsync();
    void OnPortalAssetsLoaded();

    /** RPCs para multiplayer */
    UFUNCTION(Server, Reliable)
    void ServerActivatePortal(float Duration, float Intensity);

    UFUNCTION(NetMulticast, Reliable)
    void MulticastTriggerPortalEffect(float SpawnProbability);

    UFUNCTION(Server, Reliable)
    void ServerSpawnHighRiskReward(float RewardTier);

    /** Validações server-side anti-cheat */
    bool ValidatePortalActivation(float Duration, float Intensity) const;
    bool ValidateEffectTrigger(float SpawnProbability) const;

    /** Integração com Trilhos Solar/Axis/Lunar */
    void UpdateTrailIntegration();
    FVector GetNearestTrailIntersection() const;
};